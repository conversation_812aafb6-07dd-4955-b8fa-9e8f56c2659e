<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Checkout page for {{ $landingPage->product->name }}">
    <meta name="keywords" content="checkout, product, {{ $landingPage->product->name }}">
    <meta name="author" content="Jualinn">
    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow">
    <meta name="googlebot" content="index, follow">
    <meta property="og:type" content="product">
    <meta property="og:title" content="Jualinn - {{ $landingPage->product->name }}">
    <meta property="og:description" content="{{ $landingPage->product->description }}">
    <meta property="og:image"
          content="https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:site_name" content="Jualinn">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Jualinn - {{ $landingPage->product->name }}">
    <meta name="twitter:description" content="{{ $landingPage->product->description }}">
    <meta name="twitter:image"
          content="https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg">
    <meta name="twitter:site" content="@jualinn">
    <link rel="canonical" href="{{ url()->current() }}">
    <meta itemprop="name" content="Jualinn - {{ $landingPage->product->name }}">
    <meta itemprop="description" content="{{ $landingPage->product->description }}">
    <meta itemprop="image"
          content="https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg">
    <link rel="icon" href="{{ asset('assets/images/favicon-32x32.png') }}" type="image/png">
    <title>{{ $landingPage->name }}</title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/boxicons/2.1.4/css/boxicons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.21.2/dist/sweetalert2.min.css">

    <link rel="stylesheet" href="{{ asset('assets/css/landing-page.css') }}">

    <!-- Scripts -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="{{ asset('js/number_format.js') }}"></script>

    @vite('resources/css/app.css')

</head>

<body>
    <!-- Section 1 - Main Hero -->
    @if ($landingPage->product || $landingPage->main_title)
        <section class="section section-1" style="background-color: {{ $landingPage->background_color ?? '#DCE8FD' }}">
            <div class="container">
                <div class="section-content">
                    <div class="row justify-content-center">
                        <div class="col-12 col-lg-10 col-xl-8 text-center">
                            <h1 class="section-title">{{ $landingPage->main_title ?? 'JUDUL LANDING PAGE' }}</h1>
                            <p class="section-subtitle">{{ $landingPage->sub_title ?? 'Sub Judul Section 1' }}</p>
                            @if ($landingPage->product)
                                <p class="section-description">{{ $landingPage->product->name }}</p>
                            @endif
                            @if ($landingPage->content_description)
                                <div class="section-description">
                                    {!! $landingPage->content_description !!}
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="row justify-content-center mt-3 mt-md-4">
                        <div class="col-12 col-md-10 col-lg-8 col-xl-6">
                            @if ($landingPage->section_image)
                                <img src="@imageUrl($landingPage->section_image)" alt="Section 1" class="section-image w-100" loading="lazy">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center section-image"
                                     style="height: 300px; min-height: 250px;">
                                    <span class="text-muted">Gambar Section 1</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif

    <!-- Section 2 - Always check for content (no enabled flag) -->
    @php
        $section2Product = $landingPage->section2Product;
        $section2Data = [
            'product' => $section2Product,
            'background_color' => $landingPage->section2_background_color,
            'title' => $landingPage->section2_title,
            'sub_title' => $landingPage->section2_sub_title,
            'image' => $landingPage->section2_image,
            'content' => $landingPage->section2_content,
        ];
        $hasSection2Content = $section2Product || $section2Data['title'] || $section2Data['content'] || $section2Data['image'];
    @endphp

    @if ($hasSection2Content)
        <section class="section" style="background-color: {{ $section2Data['background_color'] ?? '#DCE8FD' }}">
            <div class="container">
                <div class="section-content">
                    <!-- Text on left, Image on right for section 2 -->
                    <div class="row align-items-center">
                        <div class="col-12 col-lg-6 order-2 order-lg-1">
                            <div class="section-content-text">
                                @if ($section2Data['title'])
                                    <h2 class="section-title">{{ $section2Data['title'] }}</h2>
                                @endif

                                @if ($section2Data['sub_title'])
                                    <p class="section-subtitle">{{ $section2Data['sub_title'] }}</p>
                                @endif

                                @if ($section2Product)
                                    <p class="section-description"><strong>{{ $section2Product->name }}</strong></p>
                                    @if ($section2Product->has_discount)
                                        <div class="price-info mb-3">
                                            <div
                                                 class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-2">
                                                <span class="original-price text-decoration-line-through text-muted">Rp
                                                    {{ number_format($section2Product->regular_price, 0, ',', '.') }}</span>
                                                <span class="discount-price text-danger fw-bold fs-4">Rp
                                                    {{ number_format($section2Product->discount_price, 0, ',', '.') }}</span>
                                            </div>
                                        </div>
                                    @else
                                        <div class="price-info mb-3">
                                            <span class="current-price text-primary fw-bold fs-4">Rp
                                                {{ number_format($section2Product->regular_price, 0, ',', '.') }}</span>
                                        </div>
                                    @endif
                                @endif

                                @if ($section2Data['content'])
                                    <div class="section-description">
                                        {!! $section2Data['content'] !!}
                                    </div>
                                @endif

                                <div class="mt-3 mt-md-4 d-grid d-sm-block">
                                    <a href="#checkout" class="cta-button">
                                        <i class="bx bx-shopping-cart me-2"></i>Pesan Sekarang
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 order-1 order-lg-2 mb-4 mb-lg-0">
                            @if ($section2Data['image'])
                                <img src="@imageUrl($section2Data['image'])" alt="Section 2" class="section-image w-100"
                                     loading="lazy">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center section-image"
                                     style="height: 300px; min-height: 250px;">
                                    <span class="text-muted">Gambar Section 2</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif

    <!-- Sections 3-5 (with enabled flags) -->
    @for ($i = 3; $i <= 5; $i++)
        @php
            $sectionProduct = $landingPage->{"section{$i}Product"};
            $sectionData = [
                'product' => $sectionProduct,
                'background_color' => $landingPage->{"section{$i}_background_color"},
                'title' => $landingPage->{"section{$i}_title"},
                'sub_title' => $landingPage->{"section{$i}_sub_title"},
                'image' => $landingPage->{"section{$i}_image"},
                'content' => $landingPage->{"section{$i}_content"},
            ];
            $isEnabled = $landingPage->{"section{$i}_enabled"};
            $hasContent = $sectionProduct || $sectionData['title'] || $sectionData['content'] || $sectionData['image'];
        @endphp

        @if ($isEnabled && $hasContent)
            <section class="section" style="background-color: {{ $sectionData['background_color'] ?? '#DCE8FD' }}">
                <div class="container">
                    <div class="section-content">
                        @if ($i == 4)
                            <!-- Text on left, Image on right for section 4 -->
                            <div class="row align-items-center">
                                <div class="col-lg-6">
                                    <div class="section-content-text">
                                        @if ($sectionData['title'])
                                            <h2 class="section-title">{{ $sectionData['title'] }}</h2>
                                        @endif

                                        @if ($sectionData['sub_title'])
                                            <p class="section-subtitle">{{ $sectionData['sub_title'] }}</p>
                                        @endif

                                        @if ($sectionProduct)
                                            <p class="section-description">
                                                <strong>{{ $sectionProduct->name }}</strong>
                                            </p>
                                            @if ($sectionProduct->has_discount)
                                                <div class="price-info mb-3">
                                                    <span
                                                          class="original-price text-decoration-line-through text-muted me-2">Rp
                                                        {{ number_format($sectionProduct->regular_price, 0, ',', '.') }}</span>
                                                    <span class="discount-price text-danger fw-bold fs-4">Rp
                                                        {{ number_format($sectionProduct->discount_price, 0, ',', '.') }}</span>
                                                </div>
                                            @else
                                                <div class="price-info mb-3">
                                                    <span class="current-price text-primary fw-bold fs-4">Rp
                                                        {{ number_format($sectionProduct->regular_price, 0, ',', '.') }}</span>
                                                </div>
                                            @endif
                                        @endif

                                        @if ($sectionData['content'])
                                            <div class="section-description">
                                                {!! $sectionData['content'] !!}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    @if ($sectionData['image'])
                                        <img src="@imageUrl($sectionData['image'])" alt="Section {{ $i }}"
                                             class="section-image w-100">
                                    @else
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center section-image"
                                             style="height: 400px;">
                                            <span class="text-muted">Gambar Section {{ $i }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @else
                            <!-- Image on left, Text on right for sections 3 and 5 -->
                            <div class="row align-items-center">
                                <div class="col-lg-6">
                                    @if ($sectionData['image'])
                                        <img src="@imageUrl($sectionData['image'])" alt="Section {{ $i }}"
                                             class="section-image w-100">
                                    @else
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center section-image"
                                             style="height: 400px;">
                                            <span class="text-muted">Gambar Section {{ $i }}</span>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-lg-6">
                                    <div class="section-content-text">
                                        @if ($sectionData['title'])
                                            <h2 class="section-title">{{ $sectionData['title'] }}</h2>
                                        @endif

                                        @if ($sectionData['sub_title'])
                                            <p class="section-subtitle">{{ $sectionData['sub_title'] }}</p>
                                        @endif

                                        @if ($sectionProduct)
                                            <p class="section-description">
                                                <strong>{{ $sectionProduct->name }}</strong>
                                            </p>
                                            @if ($sectionProduct->has_discount)
                                                <div class="price-info mb-3">
                                                    <span
                                                          class="original-price text-decoration-line-through text-muted me-2">Rp
                                                        {{ number_format($sectionProduct->regular_price, 0, ',', '.') }}</span>
                                                    <span class="discount-price text-danger fw-bold fs-4">Rp
                                                        {{ number_format($sectionProduct->discount_price, 0, ',', '.') }}</span>
                                                </div>
                                            @else
                                                <div class="price-info mb-3">
                                                    <span class="current-price text-primary fw-bold fs-4">Rp
                                                        {{ number_format($sectionProduct->regular_price, 0, ',', '.') }}</span>
                                                </div>
                                            @endif
                                        @endif

                                        @if ($sectionData['content'])
                                            <div class="section-description">
                                                {!! $sectionData['content'] !!}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </section>
        @endif
    @endfor

    <!-- Section Separator -->
    <div class="container">
        <div class="section-separator"></div>
    </div>

    <!-- Section 6 - Checkout Form -->
    @if ($landingPage->section6)
        <form id="form-checkout">
            <section id="checkout" class="checkout-section"
                     style="background-color: {{ $landingPage->section6->background_color ?? '#DCE8FD' }}">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-12 col-lg-10 col-xl-8">
                            <div class="checkout-form">
                                @if ($landingPage->section6->product)
                                    <h2 class="product-name">{{ $landingPage->section6->product->name }}</h2>
                                @else
                                    <h2 class="product-name">FORM PEMESANAN</h2>
                                @endif
                                {{-- DATA PESANAN --}}
                                <div class="mb-8 p-4 bg-gray-50 rounded-lg shadow-sm">
                                    <h3 class="text-lg font-semibold mb-4 text-gray-800"><i
                                           class="bx bx-package me-2"></i>Data Pesanan</h3>
                                    <div class="space-y-4">
                                        <div class="flex items-center mb-4">
                                            <img src="{{ asset('storage/' . $landingPage->section_image) }}"
                                                 alt="Gambar produk"
                                                 class="w-24 h-24 rounded-md mr-4 object-cover shadow-sm"
                                                 width="64" height="64" />
                                            <div>
                                                <h4 class="font-semibold text-gray-800">
                                                    {{ $landingPage->product->name }}
                                                </h4>
                                            </div>
                                        </div>
                                        <div class="flex justify-between items-center text-gray-700 mb-2">
                                            <span>Harga Satuan</span>
                                            <span class="font-semibold text-gray-800">Rp
                                                {{ number_format($landingPage->product->regular_price, 0, ',', '.') }}</span>
                                        </div>
                                        <div class="flex justify-between items-center text-gray-700 mb-2">
                                            <span>Jumlah</span>
                                            <div class="flex items-center space-x-2">
                                                <button type="button" id="decrementQty"
                                                        class="px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 text-lg font-bold">-</button>
                                                <input type="number" id="quantity" name="quantity" min="1"
                                                       value="1"
                                                       class="w-8 text-center border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-red-500" />
                                                <button type="button" id="incrementQty"
                                                        class="px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 text-lg font-bold">+</button>
                                            </div>
                                        </div>
                                        <!-- Payment Method Selection -->
                                        <div class="mb-8">
                                            <h3 class="text-lg font-semibold mb-4 text-gray-800">Metode Pembayaran</h3>
                                            <div
                                                 class="flex border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                                                <button type="button"
                                                        class="payment-button w-1/2 py-3 text-center font-semibold transition-colors duration-200 active"
                                                        id="codButton">COD (Bayar di Tempat)</button>
                                                <button type="button"
                                                        class="payment-button w-1/2 py-3 text-center font-semibold text-gray-600 bg-white transition-colors duration-200"
                                                        id="transferButton">Transfer Bank</button>
                                            </div>
                                            <!-- Add payment method details/instructions here based on selection -->
                                            <div id="paymentDetails"
                                                 class="mt-4 p-4 bg-blue-50 border border-blue-200 text-blue-800 rounded-lg text-sm hidden shadow-inner">
                                                <!-- Content will be added by JavaScript -->
                                            </div>
                                        </div>
                                        <!-- Order Summary (Subtotal, Shipping, Fees) -->
                                        <div class="mb-8 p-4 bg-gray-50 rounded-lg shadow-sm">
                                            <h3 class="text-lg font-semibold mb-4 text-gray-800">Rincian Pembayaran
                                            </h3>
                                            <div class="flex justify-between items-center mb-2 text-gray-700">
                                                <span>Subtotal Produk</span>
                                                <span class="font-semibold text-gray-800" id="subtotalAmount">Rp.
                                                    {{ number_format($landingPage->product->regular_price, 0, ',', '.') }}</span>
                                            </div>
                                            <div class="flex justify-between items-center mb-2 text-gray-700">
                                                <span>Ongkos Kirim</span>
                                                <span id="shippingCost" class="text-gray-500 italic">Hitung setelah
                                                    alamat</span>
                                                <input type="hidden" name="shipping_cost" id="shippingCostInput">
                                            </div>
                                            <div class="flex justify-between items-center mb-2 text-gray-700"
                                                 id="codFeeRow">
                                                <span>Biaya COD</span>
                                                <span id="codFee" class="text-gray-500 italic">Hitung setelah
                                                    alamat</span>
                                            </div>
                                            <div class="border-t border-gray-300 my-4"></div>
                                            <div
                                                 class="flex justify-between items-center font-bold text-xl text-gray-800">
                                                <span>Total Bayar</span>
                                                <span id="totalAmount">Rp.
                                                    {{ number_format($landingPage->product->regular_price, 0, ',', '.') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                {{-- DATA DIRI PEMESAN --}}
                                <div class="mb-8 p-4 bg-gray-50 rounded-lg shadow-sm">
                                    <h3 class="text-lg font-semibold mb-4 text-gray-800"><i
                                           class="bx bx-user me-2"></i>Data Diri Pemesan</h3>
                                    <div class="space-y-4">
                                        <div>
                                            <label for="receiverName"
                                                   class="block mb-2 text-sm font-semibold text-gray-700">{{ $landingPage->section6->customer_name_label ?? 'Nama Lengkap' }}:</label>
                                            <input type="text" id="receiverName" name="receiverName"
                                                   class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"
                                                   placeholder="{{ $landingPage->section6->customer_name_placeholder ?? 'Masukkan nama lengkap Anda' }}"
                                                   required
                                                   minlength="2"
                                                   maxlength="100"
                                                   pattern="[a-zA-Z\s]+"
                                                   aria-describedby="receiverName-error"
                                                   autocomplete="name" />
                                            <div id="receiverName-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                        </div>
                                        <div>
                                            <label for="whatsappNumber"
                                                   class="block mb-2 text-sm font-semibold text-gray-700">{{ $landingPage->section6->whatsapp_label ?? 'Nomor WhatsApp' }}</label>
                                            <input type="tel" id="whatsappNumber" name="whatsappNumber"
                                                   class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"
                                                   placeholder="{{ $landingPage->section6->whatsapp_placeholder ?? 'Contoh: 08123456789' }}"
                                                   required
                                                   minlength="10"
                                                   maxlength="15"
                                                   pattern="^(\+62|62|0)[0-9]{8,13}$"
                                                   aria-describedby="whatsappNumber-error"
                                                   autocomplete="tel" />
                                            <div id="whatsappNumber-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                        </div>

                                        <label
                                               class="block mb-2 text-sm font-semibold text-gray-700">{{ $landingPage->section6->address_label ?? 'Alamat Lengkap' }}</label>

                                        <div>
                                            <div x-data="getAddress()" x-init="init()" class="space-y-4">
                                                <!-- Dropdown Provinsi -->
                                                <div class="relative">
                                                    <div @click="open = !open"
                                                         class="w-full p-2 border border-gray-300 rounded-md cursor-pointer flex justify-between items-center bg-white">
                                                        <span x-text="selectedName || 'Pilih Provinsi'"
                                                              class="text-gray-700"></span>
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                             viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                  stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </div>

                                                    <div x-show="open" @click.away="open = false"
                                                         class="absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                                        <input type="text" x-model="search" @click.stop
                                                               placeholder="Cari provinsi..."
                                                               class="w-full p-2 border-b border-gray-300 focus:outline-none">

                                                        <div class="max-h-60 overflow-y-auto">
                                                            <template x-for="province in filteredProvinces"
                                                                      :key="province.id">
                                                                <div @click="selectProvince(province)"
                                                                     class="p-2 hover:bg-gray-100 cursor-pointer"
                                                                     x-text="province.provinsi_name">
                                                                </div>
                                                            </template>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="province" x-model="selected">
                                                    <div id="province-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                                </div>

                                                <!-- Dropdown Kabupaten -->
                                                <div class="relative">
                                                    <div @click="openKabupaten = !openKabupaten"
                                                         class="w-full p-2 border border-gray-300 rounded-md cursor-pointer flex justify-between items-center bg-white">
                                                        <span x-text="selectedNameKabupaten || 'Pilih Kabupaten/Kota'"
                                                              class="text-gray-700"></span>
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                             viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                  stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </div>

                                                    <div x-show="openKabupaten" @click.away="openKabupaten = false"
                                                         class="absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                                        <input type="text" x-model="searchKabupaten" @click.stop
                                                               placeholder="Cari kabupaten..."
                                                               class="w-full p-2 border-b border-gray-300 focus:outline-none">

                                                        <div class="max-h-60 overflow-y-auto">
                                                            <template x-for="kab in filteredKabupaten"
                                                                      :key="kab.id">
                                                                <div @click="selectKabupaten(kab)"
                                                                     class="p-2 hover:bg-gray-100 cursor-pointer"
                                                                     x-text="kab.kabupaten_name">
                                                                </div>
                                                            </template>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="kabupaten"
                                                           x-model="selectedKabupaten">
                                                    <div id="kabupaten-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                                </div>

                                                <!-- Dropdown Kecamatan -->
                                                <div class="relative">
                                                    <div @click="openKecamatan = !openKecamatan"
                                                         class="w-full p-2 border border-gray-300 rounded-md cursor-pointer flex justify-between items-center bg-white">
                                                        <span x-text="selectedNameKecamatan || 'Pilih Kecamatan'"
                                                              class="text-gray-700"></span>
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                             viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                  stroke-width="2" d="M19 9l-7 7-7-7">
                                                            </path>
                                                        </svg>
                                                    </div>

                                                    <div x-show="openKecamatan" @click.away="openKecamatan = false"
                                                         class="absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                                        <input type="text" x-model="searchKecamatan" @click.stop
                                                               placeholder="Cari Kecamatan..."
                                                               class="w-full p-2 border-b border-gray-300 focus:outline-none">

                                                        <div class="max-h-60 overflow-y-auto">
                                                            <template x-for="kec in filteredKecamatan"
                                                                      :key="kec.id">
                                                                <div @click="selectKecamatan(kec)"
                                                                     class="p-2 hover:bg-gray-100 cursor-pointer"
                                                                     x-text="kec.kecamatan_name">
                                                                </div>
                                                            </template>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="kecamatan"
                                                           x-model="selectedKecamatan">
                                                    <div id="kecamatan-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                                </div>

                                                <!-- Dropdown Kelurahan -->
                                                <div class="relative">
                                                    <div @click="openKelurahan = !openKelurahan"
                                                         class="w-full p-2 border border-gray-300 rounded-md cursor-pointer flex justify-between items-center bg-white">
                                                        <span x-text="selectedNameKelurahan || 'Pilih Kelurahan'"
                                                              class="text-gray-700"></span>
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                             viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                  stroke-width="2" d="M19 9l-7 7-7-7">
                                                            </path>
                                                        </svg>
                                                    </div>

                                                    <div x-show="openKelurahan" @click.away="openKelurahan = false"
                                                         class="absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                                        <input type="text" x-model="searchKelurahan" @click.stop
                                                               placeholder="Cari Kelurahan..."
                                                               class="w-full p-2 border-b border-gray-300 focus:outline-none">

                                                        <div class="max-h-60 overflow-y-auto">
                                                            <template x-for="kel in filteredKelurahan"
                                                                      :key="kel.id">
                                                                <div @click="selectKelurahan(kel)"
                                                                     class="p-2 hover:bg-gray-100 cursor-pointer"
                                                                     x-text="kel.kelurahan_name">
                                                                </div>
                                                            </template>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="kelurahan"
                                                           x-model="selectedKelurahan">
                                                    <div id="kelurahan-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                                </div>

                                            </div>
                                        </div>

                                        <div>
                                            <label for="addressDetails" class="block mb-2 text-sm font-semibold text-gray-700">Detail Alamat:</label>
                                            <textarea id="addressDetails" name="addressDetails"
                                                      class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors duration-200"
                                                      placeholder="{{ $landingPage->section6->address_placeholder ?? 'Nama Jalan, Nomor Rumah, RT/RW, Patokan (cth: dekat masjid)' }}"
                                                      rows="3"
                                                      required
                                                      minlength="10"
                                                      maxlength="500"
                                                      aria-describedby="addressDetails-error"></textarea>
                                            <div id="addressDetails-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                        </div>

                                        <!-- Loading State -->
                                        <div id="form-loading" class="hidden text-center py-4">
                                            <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-red-500 transition ease-in-out duration-150">
                                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                Memproses pesanan...
                                            </div>
                                        </div>

                                        <button type="submit" id="submit-button"
                                                class="w-full py-4 bg-red-600 text-white font-bold text-lg rounded-md hover:bg-red-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed">
                                            {{ $landingPage->section6->order_button_text ?? 'Buat Pesanan Sekarang' }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </form>
    @endif

    <!-- Floating CTA Button for Mobile -->
    <a href="#checkout" class="floating-cta" id="floatingCta">
        <i class="bx bx-shopping-cart me-2"></i>Pesan Sekarang
    </a>

    {{-- Debug Information --}}
    @include('landing-page.partials.debug-info')

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.21.2/dist/sweetalert2.all.min.js"></script>
    <script type="application/ld+json">
        {
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": "{{ $landingPage->product->name }}",
            "image": [
                "https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg"
            ],
            "description": "{{ $landingPage->product->description }}",
            "brand": {
                "@type": "Brand",
                "name": "Jualinn"
            },
            "offers": {
                "@type": "Offer",
                "priceCurrency": "IDR",
                "price": "{{ $landingPage->product->regular_price }}",
                "availability": "https://schema.org/InStock",
                "url": "{{ url()->current() }}"
            }
        }
        </script>
    @if ($landingPage->facebookPixel)
        <!-- Facebook Pixel Code - IMPLEMENTASI MANUAL SEDERHANA -->
        <script>
            ! function(f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function() {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');

            // Initialize Pixel
            fbq('init', '{{ $landingPage->facebookPixel->pixel_id }}');

            // Track PageView (otomatis)
            fbq('track', 'PageView');

            // MANUAL EVENT 1: ViewContent - Ketika halaman dimuat
            fbq('track', 'ViewContent', {
                content_name: '{{ $landingPage->product->name ?? 'Landing Page Product' }}',
                content_ids: ['{{ $landingPage->product->id ?? '1' }}'],
                content_type: 'product',
                value: {{ $landingPage->product->regular_price ?? 0 }},
                currency: 'IDR'
            });
        </script>
        <noscript>
            <img height="1" width="1" style="display:none"
                 src="https://www.facebook.com/tr?id={{ $landingPage->facebookPixel->pixel_id }}&ev=PageView&noscript=1" />
        </noscript>
        <!-- End Facebook Pixel Code -->
    @endif
    <script>
        $(document).ready(function() {
            // Mobile responsive enhancements
            let isMobile = window.innerWidth <= 767;
            let isTablet = window.innerWidth > 767 && window.innerWidth <= 991;

            // Handle window resize
            $(window).on('resize', function() {
                isMobile = window.innerWidth <= 767;
                isTablet = window.innerWidth > 767 && window.innerWidth <= 991;
                handleFloatingButton();
            });

            // Floating CTA button functionality
            function handleFloatingButton() {
                const $floatingCta = $('#floatingCta');
                const $checkoutSection = $('#checkout');

                if (!isMobile || !$checkoutSection.length) {
                    $floatingCta.hide();
                    return;
                }

                $(window).on('scroll', function() {
                    const scrollTop = $(window).scrollTop();
                    const checkoutOffset = $checkoutSection.offset().top;
                    const windowHeight = $(window).height();

                    // Show floating button when not near checkout section
                    if (scrollTop + windowHeight < checkoutOffset - 100) {
                        $floatingCta.addClass('show');
                    } else {
                        $floatingCta.removeClass('show');
                    }
                });
            }

            // Initialize floating button
            handleFloatingButton();

            // PIXEL TRACKING SUDAH DILAKUKAN DI HEAD SECTION
            // ViewContent event sudah di-track otomatis saat halaman dimuat

            // Payment option selection
            $('.payment-option').on('click', function() {
                $('.payment-option').removeClass('active');
                $(this).addClass('active');
                $(this).find('input[type="radio"]').prop('checked', true);
            });

            // Enhanced smooth scrolling for CTA buttons
            $('a[href="#checkout"], .floating-cta').on('click', function(e) {
                e.preventDefault();
                const target = $('#checkout');
                if (target.length) {
                    const offset = isMobile ? 20 : 50;
                    const scrollDuration = isMobile ? 600 : 800;

                    $('html, body').animate({
                        scrollTop: target.offset().top - offset
                    }, scrollDuration, 'swing');

                    // Hide floating button after click
                    if ($(this).hasClass('floating-cta')) {
                        $(this).removeClass('show');
                    }
                }

                // TIDAK PERLU TRACKING INITIATE CHECKOUT - HANYA FOCUS PADA 2 EVENT UTAMA
            });

            // Touch-friendly form interactions
            if (isMobile) {
                // Prevent zoom on input focus for iOS
                $('input[type="text"], input[type="tel"], input[type="email"], textarea, select').attr(
                    'autocomplete', 'on');

                // Add touch feedback for buttons
                $('.cta-button, .order-button, .scroll-button, .payment-option').on('touchstart', function() {
                    $(this).addClass('active');
                }).on('touchend touchcancel', function() {
                    $(this).removeClass('active');
                });
            }

            // Form submission
            $('#checkoutForm').on('submit', function(e) {
                e.preventDefault();

                // Get form data
                const formData = new FormData(this);

                @if ($landingPage->facebookPixel)
                    // MANUAL EVENT 2: Purchase - Ketika form di-submit
                    fbq('track', 'Purchase', {
                        content_name: '{{ $landingPage->section6->product->name ?? 'Landing Page Product' }}',
                        content_ids: ['{{ $landingPage->section6->product->id ?? '1' }}'],
                        content_type: 'product',
                        value: {{ $landingPage->section6->product_price ?? ($landingPage->product->regular_price ?? 0) }},
                        currency: 'IDR'
                    });
                @endif

                // Create WhatsApp message
                let message = `*Pesanan Baru dari Landing Page*\n\n`;
                message += `*Nama:* ${formData.get('customer_name')}\n`;
                message += `*WhatsApp:* ${formData.get('whatsapp')}\n`;
                message += `*Alamat:* ${formData.get('address')}\n`;
                message += `*Kota:* ${formData.get('city')}\n`;
                if (formData.get('notes')) {
                    message += `*Catatan:* ${formData.get('notes')}\n`;
                }
                message += `*Pengiriman:* ${formData.get('shipping')}\n`;
                message += `*Pembayaran:* ${formData.get('payment_method')}\n\n`;
                @if ($landingPage->section6->product)
                    message += `*Produk:* {{ $landingPage->section6->product->name }}\n`;
                @endif
                message += `*Total:* {{ $landingPage->section6->formatted_total_price }}`;

                // Open WhatsApp
                const whatsappUrl = `https://wa.me/6281234567890?text=${encodeURIComponent(message)}`;
                window.open(whatsappUrl, '_blank');
            });
        });

        // Enhanced function to scroll to section 1
        function scrollToSection1() {
            const scrollDuration = isMobile ? 600 : 800;
            $('html, body').animate({
                scrollTop: 0
            }, scrollDuration, 'swing');
        }

        // Lazy loading for images (if supported)
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            observer.unobserve(img);
                        }
                    }
                });
            });

            // Observe all images with data-src attribute
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Performance optimization: Debounce scroll events
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Apply debounced scroll handler
        $(window).on('scroll', debounce(function() {
            // Scroll handlers are already defined above
        }, 10));
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const codButton = document.getElementById('codButton');
            const transferButton = document.getElementById('transferButton');
            const codFeeRow = document.getElementById('codFeeRow');
            const paymentDetailsDiv = document.getElementById('paymentDetails');
            const paymentButtons = document.querySelectorAll('.payment-button');

            function activateButton(button) {
                paymentButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-red-600', 'text-white');
                    btn.classList.add('bg-white', 'text-gray-600');
                });
                button.classList.add('active', 'bg-red-600', 'text-white');
                button.classList.remove('bg-white', 'text-gray-600');
            }

            // Initial state (COD selected)
            activateButton(codButton);
            codFeeRow.classList.remove('hidden');
            paymentDetailsDiv.classList.add('hidden'); // Hide payment details initially

            codButton.addEventListener('click', function() {
                activateButton(codButton);
                codFeeRow.classList.remove('hidden'); // Show COD fee
                paymentDetailsDiv.classList.add('hidden'); // Hide payment details
                // Update total amount calculation if needed
            });

            transferButton.addEventListener('click', function() {
                activateButton(transferButton);
                codFeeRow.classList.add('hidden'); // Hide COD fee
                paymentDetailsDiv.classList.remove('hidden'); // Show payment details
                paymentDetailsDiv.innerHTML = `
                    <p class="font-semibold mb-2">Instruksi Transfer Bank:</p>
                    <p>Silakan transfer ke rekening:</p>
                    <p>Bank: [Nama Bank]</p>
                    <p>Nomor Rekening: [Nomor Rekening Anda]</p>
                    <p>Atas Nama: [Nama Pemilik Rekening]</p>
                    <p class="mt-3">Jumlah yang harus ditransfer: <span class="font-bold text-lg" id="transferAmount">Rp 100.000</span></p>
                    <p class="text-xs mt-3 text-blue-700">Pesanan akan diproses setelah konfirmasi pembayaran. Mohon transfer sesuai jumlah total.</p>
                `;
                // Update total amount calculation if needed (remove COD fee)
                // This is a simple example, actual calculation should happen here
                document.getElementById('transferAmount').innerText = document.getElementById('totalAmount')
                    .innerText;
            });

        });
        document.addEventListener('DOMContentLoaded', function() {
            const qtyInput = document.getElementById('quantity');
            document.getElementById('decrementQty').onclick = function() {
                let val = parseInt(qtyInput.value) || 1;
                if (val > 1) qtyInput.value = val - 1;
                subtotalAmount.innerText = 'Rp. ' + (val - 1) * {{ $landingPage->product->regular_price }};
                totalAmount.innerText = 'Rp. ' + (val - 1) * {{ $landingPage->product->regular_price }};
                // Update total amount calculation if needed (remove COD fee)
                // This is a simple example, actual calculation should happen here
                document.getElementById('subtotalAmount').innerText = 'Rp. ' + number_format((val - 1) *
                    {{ $landingPage->product->regular_price }});
                document.getElementById('totalAmount').innerText = 'Rp. ' + number_format((val - 1) *
                    {{ $landingPage->product->regular_price }});
            };
            document.getElementById('incrementQty').onclick = function() {
                let val = parseInt(qtyInput.value) || 1;
                qtyInput.value = val + 1;
                subtotalAmount.innerText = 'Rp. ' + (val + 1) * {{ $landingPage->product->regular_price }};
                totalAmount.innerText = 'Rp. ' + (val + 1) * {{ $landingPage->product->regular_price }};
                // Update total amount calculation if needed (remove COD fee)
                // This is a simple example, actual calculation should happen here
                document.getElementById('subtotalAmount').innerText = 'Rp. ' + number_format((val + 1) *
                    {{ $landingPage->product->regular_price }});
                document.getElementById('totalAmount').innerText = 'Rp. ' + number_format((val + 1) *
                    {{ $landingPage->product->regular_price }});
            };
        });
        document.addEventListener('DOMContentLoaded', function() {
            const mainImage = document.getElementById('mainProductImage');
            document.querySelectorAll('.thumbnail-image').forEach(function(thumb) {
                thumb.addEventListener('click', function() {
                    if (mainImage) {
                        mainImage.src = this.getAttribute('data-image');
                    }
                });
            });
        });
    </script>
    <script>
        // Form Validation Utilities
        const FormValidator = {
            // Validation rules
            rules: {
                receiverName: {
                    required: true,
                    minLength: 2,
                    maxLength: 100,
                    pattern: /^[a-zA-Z\s]+$/,
                    message: 'Nama harus berisi 2-100 karakter dan hanya huruf'
                },
                whatsappNumber: {
                    required: true,
                    pattern: /^(\+62|62|0)[0-9]{8,13}$/,
                    message: 'Nomor WhatsApp tidak valid (contoh: 08123456789)'
                },
                addressDetails: {
                    required: true,
                    minLength: 10,
                    maxLength: 500,
                    message: 'Alamat harus berisi 10-500 karakter'
                },
                province: {
                    required: true,
                    message: 'Provinsi harus dipilih'
                },
                kabupaten: {
                    required: true,
                    message: 'Kabupaten/Kota harus dipilih'
                },
                kecamatan: {
                    required: true,
                    message: 'Kecamatan harus dipilih'
                },
                kelurahan: {
                    required: true,
                    message: 'Kelurahan harus dipilih'
                }
            },

            // Validate single field
            validateField(fieldName, value) {
                const rule = this.rules[fieldName];
                if (!rule) return {
                    isValid: true
                };

                // Required check
                if (rule.required && (!value || value.trim() === '')) {
                    return {
                        isValid: false,
                        message: rule.message
                    };
                }

                // Skip other validations if field is empty and not required
                if (!value || value.trim() === '') {
                    return {
                        isValid: true
                    };
                }

                const trimmedValue = value.trim();

                // Length checks
                if (rule.minLength && trimmedValue.length < rule.minLength) {
                    return {
                        isValid: false,
                        message: rule.message
                    };
                }
                if (rule.maxLength && trimmedValue.length > rule.maxLength) {
                    return {
                        isValid: false,
                        message: rule.message
                    };
                }

                // Pattern check
                if (rule.pattern && !rule.pattern.test(trimmedValue)) {
                    return {
                        isValid: false,
                        message: rule.message
                    };
                }

                return {
                    isValid: true
                };
            },

            // Show field error
            showFieldError(fieldName, message) {
                const field = document.getElementById(fieldName);
                const errorDiv = document.getElementById(fieldName + '-error');

                if (field && errorDiv) {
                    field.classList.add('border-red-500');
                    field.classList.remove('border-gray-300');
                    errorDiv.textContent = message;
                    errorDiv.classList.remove('hidden');
                }
            },

            // Clear field error
            clearFieldError(fieldName) {
                const field = document.getElementById(fieldName);
                const errorDiv = document.getElementById(fieldName + '-error');

                if (field && errorDiv) {
                    field.classList.remove('border-red-500');
                    field.classList.add('border-gray-300');
                    errorDiv.textContent = '';
                    errorDiv.classList.add('hidden');
                }
            },

            // Validate all form fields
            validateForm() {
                const errors = [];

                // Get form values
                const formData = {
                    receiverName: document.getElementById('receiverName')?.value || '',
                    whatsappNumber: document.getElementById('whatsappNumber')?.value || '',
                    addressDetails: document.getElementById('addressDetails')?.value || '',
                    province: document.querySelector('input[name="province"]')?.value || '',
                    kabupaten: document.querySelector('input[name="kabupaten"]')?.value || '',
                    kecamatan: document.querySelector('input[name="kecamatan"]')?.value || '',
                    kelurahan: document.querySelector('input[name="kelurahan"]')?.value || ''
                };

                // Clear all previous errors
                Object.keys(this.rules).forEach(fieldName => {
                    this.clearFieldError(fieldName);
                });

                // Validate each field
                Object.keys(formData).forEach(fieldName => {
                    const validation = this.validateField(fieldName, formData[fieldName]);
                    if (!validation.isValid) {
                        errors.push({
                            field: fieldName,
                            message: validation.message
                        });
                        this.showFieldError(fieldName, validation.message);
                    }
                });

                return errors;
            },

            // Format phone number
            formatPhoneNumber(phone) {
                // Remove all non-digits
                let cleaned = phone.replace(/\D/g, '');

                // Convert to standard format
                if (cleaned.startsWith('62')) {
                    return '+' + cleaned;
                } else if (cleaned.startsWith('0')) {
                    return '+62' + cleaned.substring(1);
                } else {
                    return '+62' + cleaned;
                }
            },

            // Sanitize text input
            sanitizeText(text) {
                return text.trim().replace(/[<>]/g, '');
            }
        };

        function getAddress() {
            return {
                // State dropdown provinsi
                open: false,
                search: '',
                selected: '',
                selectedName: '',
                provinces: [],

                // State dropdown kabupaten
                openKabupaten: false,
                selectedNameKabupaten: '',
                searchKabupaten: '',
                selectedKabupaten: '',
                kabupatenList: [],

                // State dropdown kecamatan
                openKecamatan: false,
                selectedNameKecamatan: '',
                searchKecamatan: '',
                selectedKecamatan: '',
                kecamatanList: [],

                // State dropdown kelurahan
                openKelurahan: false,
                selectedNameKelurahan: '',
                searchKelurahan: '',
                selectedKelurahan: '',
                kelurahanList: [],

                init() {
                    fetch('{{ route('get-provinsi') }}')
                        .then(res => res.json())
                        .then(data => {
                            this.provinces = data.datas;
                        })
                        .catch(err => console.error('Error fetching provinsi:', err));
                },

                get filteredProvinces() {
                    return this.provinces.filter(p =>
                        p.provinsi_name.toLowerCase().includes(this.search.toLowerCase())
                    );
                },

                get filteredKabupaten() {
                    return this.kabupatenList.filter(k =>
                        k.kabupaten_name.toLowerCase().includes(this.searchKabupaten.toLowerCase())
                    );
                },

                get filteredKecamatan() {
                    return this.kecamatanList.filter(k =>
                        k.kecamatan_name.toLowerCase().includes(this.searchKecamatan.toLowerCase())
                    );
                },

                get filteredKelurahan() {
                    return this.kelurahanList.filter(k =>
                        k.kelurahan_name.toLowerCase().includes(this.searchKelurahan.toLowerCase())
                    );
                },


                selectProvince(province) {
                    this.selected = province.id;
                    this.selectedName = province.provinsi_name;
                    this.open = false;

                    this.selectedKabupaten = '';
                    this.selectedKecamatan = '';
                    this.selectedKelurahan = '';

                    this.kabupatenList = [];
                    this.kecamatanList = [];
                    this.kelurahanList = [];

                    this.fetchKabupaten(province.id);
                },

                selectKabupaten(kabupaten) {
                    this.selectedNameKabupaten = kabupaten.kabupaten_name;
                    this.selectedKabupaten = kabupaten.id;
                    this.openKabupaten = false;

                    this.selectedKecamatan = '';
                    this.selectedKelurahan = '';

                    this.kecamatanList = [];
                    this.kelurahanList = [];

                    this.fetchKecamatan(kabupaten.id);
                },

                selectKecamatan(kecamatan) {
                    this.selectedKecamatan = kecamatan.id;
                    this.selectedNameKecamatan = kecamatan.kecamatan_name;
                    this.openKecamatan = false;

                    this.selectedKelurahan = '';
                    this.kelurahanList = [];

                    this.fetchKelurahan(kecamatan.id); // ✅ Panggil fungsi fetch kelurahan
                },

                selectKelurahan(kelurahan) {
                    this.selectedKelurahan = kelurahan.id;
                    this.selectedNameKelurahan = kelurahan.kelurahan_name;
                    this.openKelurahan = false;
                },

                fetchKabupaten(provinsiId) {
                    const url = `{{ url('get-kabupaten') }}/${provinsiId}`;
                    fetch(url)
                        .then(res => res.json())
                        .then(data => {
                            this.kabupatenList = data.datas;
                        })
                        .catch(err => console.error('Error fetching kabupaten:', err));
                },

                fetchKecamatan(kabupatenId) {
                    const url = `{{ url('get-kecamatan') }}/${kabupatenId}`;
                    fetch(url)
                        .then(res => res.json())
                        .then(data => {
                            this.kecamatanList = data.datas;
                        })
                        .catch(err => console.error('Error fetching kecamatan:', err));
                },

                fetchKelurahan(kecamatanId) {

                    const url = `{{ url('get-kelurahan') }}/${kecamatanId}`;
                    fetch(url)
                        .then(res => res.json())
                        .then(data => {
                            this.kelurahanList = data.results;
                        })
                        .catch(err => console.error('Error fetching kelurahan:', err));
                }


            }
        }

        function checkAddressCompletion() {
            const receiverName = document.getElementById('receiverName').value.trim();
            const whatsappNumber = document.getElementById('whatsappNumber').value.trim();
            const province = document.querySelector('input[name="province"]').value.trim();
            const kabupaten = document.querySelector('input[name="kabupaten"]').value.trim();
            const kecamatan = document.querySelector('input[name="kecamatan"]').value.trim();
            const kelurahan = document.querySelector('input[name="kelurahan"]').value.trim();
            const addressDetails = document.querySelector('textarea').value.trim();

            if (receiverName && whatsappNumber && province && kabupaten && kecamatan && kelurahan && addressDetails) {
                getShippingCost();
            }
        }

        // Add real-time validation event listeners
        function setupRealTimeValidation() {
            // Receiver name validation
            const receiverNameField = document.getElementById('receiverName');
            if (receiverNameField) {
                receiverNameField.addEventListener('input', function() {
                    const validation = FormValidator.validateField('receiverName', this.value);
                    if (!validation.isValid) {
                        FormValidator.showFieldError('receiverName', validation.message);
                    } else {
                        FormValidator.clearFieldError('receiverName');
                    }
                    checkAddressCompletion();
                });

                receiverNameField.addEventListener('blur', function() {
                    const validation = FormValidator.validateField('receiverName', this.value);
                    if (!validation.isValid) {
                        FormValidator.showFieldError('receiverName', validation.message);
                    }
                });
            }

            // WhatsApp number validation with formatting
            const whatsappField = document.getElementById('whatsappNumber');
            if (whatsappField) {
                whatsappField.addEventListener('input', function() {
                    // Remove non-digits for validation
                    const cleaned = this.value.replace(/\D/g, '');
                    const validation = FormValidator.validateField('whatsappNumber', this.value);

                    if (!validation.isValid && this.value.length > 0) {
                        FormValidator.showFieldError('whatsappNumber', validation.message);
                    } else {
                        FormValidator.clearFieldError('whatsappNumber');
                    }
                    checkAddressCompletion();
                });

                whatsappField.addEventListener('blur', function() {
                    if (this.value) {
                        // Format phone number on blur
                        try {
                            const formatted = FormValidator.formatPhoneNumber(this.value);
                            this.value = formatted;
                        } catch (e) {
                            // Keep original value if formatting fails
                        }
                    }

                    const validation = FormValidator.validateField('whatsappNumber', this.value);
                    if (!validation.isValid) {
                        FormValidator.showFieldError('whatsappNumber', validation.message);
                    }
                });
            }

            // Address details validation
            const addressField = document.getElementById('addressDetails');
            if (addressField) {
                addressField.addEventListener('input', function() {
                    const validation = FormValidator.validateField('addressDetails', this.value);
                    if (!validation.isValid && this.value.length > 0) {
                        FormValidator.showFieldError('addressDetails', validation.message);
                    } else {
                        FormValidator.clearFieldError('addressDetails');
                    }
                    checkAddressCompletion();
                });

                addressField.addEventListener('blur', function() {
                    const validation = FormValidator.validateField('addressDetails', this.value);
                    if (!validation.isValid) {
                        FormValidator.showFieldError('addressDetails', validation.message);
                    }
                });
            }

            // Address selection validation
            const addressFields = ['province', 'kabupaten', 'kecamatan', 'kelurahan'];
            addressFields.forEach(fieldName => {
                const field = document.querySelector(`input[name="${fieldName}"]`);
                if (field) {
                    field.addEventListener('change', function() {
                        const validation = FormValidator.validateField(fieldName, this.value);
                        if (!validation.isValid) {
                            FormValidator.showFieldError(fieldName, validation.message);
                        } else {
                            FormValidator.clearFieldError(fieldName);
                        }
                        checkAddressCompletion();
                    });
                }
            });
        }

        // Initialize real-time validation when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupRealTimeValidation);
        } else {
            setupRealTimeValidation();
        }

        function getShippingCost() {
            // Implement your shipping cost calculation logic here
            // For example, you can use an API to get the shipping cost based on the selected address
            const province = document.querySelector('input[name="province"][x-model="selected"]').value;
            const kabupaten = document.querySelector('input[name="kabupaten"][x-model="selectedKabupaten"]').value;
            const kecamatan = document.querySelector('input[name="kecamatan"][x-model="selectedKecamatan"]').value;
            const kelurahan = document.querySelector('input[name="kelurahan"][x-model="selectedKelurahan"]').value;
            const qty = document.getElementById('quantity').value;
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            fetch('{{ route('getCurier') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({
                        nilaiBarang: parseInt({{ $landingPage->product->regular_price }}) * qty,
                        insurance: 0,
                        origin: 5647,
                        subdistrict_origin: 71066,
                        kecamatan: kecamatan,
                        desa: kelurahan,
                        panjangBarang: 10, // Ganti dengan panjang barang jika ada input
                        lebarBarang: 10, // Ganti dengan lebar barang jika ada input
                        tinggiBarang: 10, // Ganti dengan tinggi barang jika ada input
                        beratBarang: 1000 // Ganti dengan berat barang jika ada input (gram)
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Misal response: { shipping_cost: 15000, cod_fee: 5000 }
                    if (data.results[0].cost !== undefined) {
                        document.getElementById('shippingCost').innerText = 'Rp. ' + number_format(data.results[0]
                            .cost);
                    }
                    if (data.results[0].setting.cod_fee_amount !== undefined) {
                        document.getElementById('codFee').innerText = 'Rp. ' + number_format(data.results[0].setting
                            .cod_fee_amount);
                    }

                    // Update total bayar sesuai metode pembayaran
                    let subtotal = parseInt({{ $landingPage->product->regular_price }}) * qty;
                    let shipping = parseInt(data.results[0].cost) || 0;
                    let codFee = parseInt(data.results[0].setting.cod_fee_amount) || 0;

                    // Cek metode pembayaran aktif
                    // Listen for payment method changes to recalculate shipping cost
                    let isCOD = document.getElementById('codButton').classList.contains('active');
                    document.getElementById('codButton').onclick = function() {
                        setTimeout(getShippingCost, 0.1);
                    };
                    document.getElementById('transferButton').onclick = function() {
                        setTimeout(getShippingCost, 0.1);
                    };


                    let total = subtotal + shipping + (isCOD ? codFee : 0);
                    $('#shippingCostInput').val(shipping + (isCOD ? codFee : 0));

                    document.getElementById('totalAmount').innerText = 'Rp. ' + number_format(total);

                    // Jika transfer bank, update juga jumlah transfer di instruksi transfer
                    if (!isCOD && document.getElementById('transferAmount')) {
                        document.getElementById('transferAmount').innerText = 'Rp. ' + number_format(total);
                    }

                })
                .catch(err => {
                    document.getElementById('shippingCost').innerText = 'Gagal menghitung ongkir';
                    document.getElementById('codFee').innerText = '-';
                });
        }

        document.getElementById('form-checkout').addEventListener('submit', function(e) {
            e.preventDefault();

            // Validate form using FormValidator
            const validationErrors = FormValidator.validateForm();

            if (validationErrors.length > 0) {
                // Focus on first error field
                const firstError = validationErrors[0];
                const firstErrorField = document.getElementById(firstError.field);
                if (firstErrorField) {
                    firstErrorField.focus();
                    firstErrorField.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }

                // Show validation summary
                const errorMessages = validationErrors.map(error => `• ${error.message}`).join('<br>');
                Swal.fire({
                    icon: 'error',
                    title: 'Data Tidak Valid',
                    html: `Mohon perbaiki kesalahan berikut:<br><br>${errorMessages}`,
                    confirmButtonColor: '#dc2626',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Get and sanitize form data
            const receiverName = FormValidator.sanitizeText(document.getElementById('receiverName').value);
            const whatsappNumber = FormValidator.formatPhoneNumber(document.getElementById('whatsappNumber').value);
            const province = document.querySelector('input[name="province"]').value.trim();
            const kabupaten = document.querySelector('input[name="kabupaten"]').value.trim();
            const kecamatan = document.querySelector('input[name="kecamatan"]').value.trim();
            const kelurahan = document.querySelector('input[name="kelurahan"]').value.trim();
            const addressDetails = FormValidator.sanitizeText(document.getElementById('addressDetails').value);
            const qty = parseInt(document.getElementById('quantity').value) || 1;
            const shipping = $('#shippingCost').text().replace('Rp. ', '').replace(/\./g, '') || 0;

            let isCOD = document.getElementById('codButton').classList.contains('active');
            const codFee = parseInt(document.getElementById('codFee').value) || 0;
            const paymentMethod = isCOD ? 'cod' : 'bank_transfer';
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Data yang akan dikirim
            const data = {
                product: "{{ $landingPage->product->name }}",
                product_code: "{{ $landingPage->product->product_code ?? '' }}",
                product_price: {{ $landingPage->product->regular_price }},
                quantity: qty,
                name: receiverName,
                phone: whatsappNumber,
                address: addressDetails,
                province: province,
                city: kabupaten,
                subdistrict: kecamatan,
                zip: kelurahan,
                payment_method: paymentMethod,
                courier: 'NINJA - STANDARD', // Isi sesuai pilihan kurir jika ada, atau kosong
                shipping_cost: $('#shippingCostInput').val(), // Isi sesuai hasil perhitungan ongkir
                gross_revenue: 0, // Isi sesuai perhitungan total sebelum potongan
                net_revenue: 0, // Isi sesuai perhitungan total setelah potongan
                weight: 1000 // Isi sesuai berat produk (gram)
            };


            // Show loading state
            const submitButton = document.getElementById('submit-button');
            const loadingDiv = document.getElementById('form-loading');

            if (submitButton) {
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            }

            if (loadingDiv) {
                loadingDiv.classList.remove('hidden');
            }

            // Submit with retry mechanism
            const submitWithRetry = async (retryCount = 0) => {
                try {
                    const response = await fetch('{{ route('checkout-order') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        },
                        body: JSON.stringify(data)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.success) {
                        await Swal.fire({
                            icon: 'success',
                            title: 'Pesanan Berhasil!',
                            html: `
                                <div class="text-left">
                                    <p class="mb-2"><strong>Nama:</strong> ${receiverName}</p>
                                    <p class="mb-2"><strong>Produk:</strong> ${result.order.product}</p>
                                    <p class="mb-4"><strong>Total:</strong> ${result.order.total_amount || 'Rp 0'}</p>
                                    <p class="text-sm text-gray-600">Anda akan diarahkan ke WhatsApp untuk konfirmasi pesanan.</p>
                                </div>
                            `,
                            confirmButtonColor: '#dc2626',
                            confirmButtonText: 'Lanjut ke WhatsApp'
                        });

                        // Open WhatsApp with order details
                        const waMessage = encodeURIComponent(
                            `Halo, saya sudah melakukan pemesanan ${result.order.product} atas nama ${result.order.name}. Mohon segera diproses ya 🙏🏻`
                        );
                        window.open(`https://wa.me/6287781359219?text=${waMessage}`, '_blank');

                        // Reset form after successful submission
                        document.getElementById('form-checkout').reset();

                    } else {
                        throw new Error(result.message || 'Gagal membuat pesanan');
                    }

                } catch (error) {
                    console.error('Submission error:', error);

                    // Retry logic for network errors
                    if (retryCount < 2 && (error.name === 'TypeError' || error.message.includes('fetch'))) {
                        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                        return submitWithRetry(retryCount + 1);
                    }

                    // Show error message
                    await Swal.fire({
                        icon: 'error',
                        title: 'Gagal Memproses Pesanan',
                        html: `
                            <div class="text-left">
                                <p class="mb-2">${error.message}</p>
                                ${retryCount > 0 ? '<p class="text-sm text-gray-600">Sudah mencoba beberapa kali, silakan coba lagi nanti.</p>' : ''}
                            </div>
                        `,
                        confirmButtonColor: '#dc2626',
                        confirmButtonText: 'OK'
                    });
                } finally {
                    // Reset loading state
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }

                    if (loadingDiv) {
                        loadingDiv.classList.add('hidden');
                    }
                }
            };

            // Execute submission
            submitWithRetry();
        });
    </script>
</body>

</html>
